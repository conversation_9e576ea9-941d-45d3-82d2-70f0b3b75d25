'use client';

import { useState, useEffect, use<PERSON>allback, Suspense } from 'react';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CpuChipIcon,
  CalendarIcon,
  FunnelIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  SparklesIcon,
  BoltIcon,
  LightBulbIcon,
  BellIcon,
  ChartPieIcon,
  HandThumbUpIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface AnalyticsData {
  summary: {
    total_requests: number;
    successful_requests: number;
    success_rate: number;
    total_cost: number;
    total_input_tokens: number;
    total_output_tokens: number;
    average_cost_per_request: number;
  };
  grouped_data: Array<{
    name: string;
    requests: number;
    cost: number;
    input_tokens: number;
    output_tokens: number;
    success_rate?: number;
    period?: string; // For time series data
  }>;
}

interface CustomApiConfig {
  id: string;
  name: string;
}

interface TimeSeriesData {
  period: string;
  cost: number;
  requests: number;
  tokens: number;
}

interface LatencyData {
  provider: string;
  avg_latency: number;
  p95_latency: number;
  requests: number;
}

interface CostAlert {
  type: 'warning' | 'danger' | 'info';
  title: string;
  message: string;
  value?: string;
}

interface UsageRecommendation {
  type: 'cost_optimization' | 'performance' | 'efficiency';
  title: string;
  description: string;
  potential_savings?: number;
  icon: any;
}

function AnalyticsPageContent() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [modelAnalytics, setModelAnalytics] = useState<AnalyticsData | null>(null);
  const [configAnalytics, setConfigAnalytics] = useState<AnalyticsData | null>(null);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [previousPeriodData, setPreviousPeriodData] = useState<AnalyticsData | null>(null);
  const [customConfigs, setCustomConfigs] = useState<CustomApiConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [timeRange, setTimeRange] = useState('30');
  const [selectedConfig, setSelectedConfig] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // Budget settings
  const [monthlyBudget, setMonthlyBudget] = useState<number>(100); // Default $100/month
  const [showBudgetSettings, setShowBudgetSettings] = useState(false);

  useEffect(() => {
    fetchCustomConfigs();
  }, []);

  const fetchCustomConfigs = async () => {
    try {
      const response = await fetch('/api/custom-configs');
      if (response.ok) {
        const configs = await response.json();
        setCustomConfigs(configs);
      }
    } catch (err) {
      console.error('Error fetching configs:', err);
    }
  };

  const fetchAnalyticsData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters for current period
      const params = new URLSearchParams();
      let currentStartDate: Date;
      let currentEndDate: Date = new Date();

      if (startDate && endDate) {
        currentStartDate = new Date(startDate);
        currentEndDate = new Date(endDate);
        params.append('startDate', currentStartDate.toISOString());
        params.append('endDate', currentEndDate.toISOString());
      } else if (timeRange) {
        currentStartDate = new Date();
        currentStartDate.setDate(currentStartDate.getDate() - parseInt(timeRange));
        params.append('startDate', currentStartDate.toISOString());
      }

      if (selectedConfig) {
        params.append('customApiConfigId', selectedConfig);
      }

      // Build query parameters for previous period (for comparison)
      const prevParams = new URLSearchParams();
      const periodLength = currentEndDate.getTime() - currentStartDate!.getTime();
      const prevStartDate = new Date(currentStartDate!.getTime() - periodLength);
      const prevEndDate = new Date(currentStartDate!.getTime());

      prevParams.append('startDate', prevStartDate.toISOString());
      prevParams.append('endDate', prevEndDate.toISOString());
      if (selectedConfig) {
        prevParams.append('customApiConfigId', selectedConfig);
      }

      // Fetch multiple analytics views
      const [
        providerResponse,
        modelResponse,
        timeSeriesResponse,
        previousPeriodResponse
      ] = await Promise.all([
        fetch(`/api/analytics/summary?${params.toString()}&groupBy=provider`),
        fetch(`/api/analytics/summary?${params.toString()}&groupBy=model`),
        fetch(`/api/analytics/summary?${params.toString()}&groupBy=day`),
        fetch(`/api/analytics/summary?${prevParams.toString()}&groupBy=day`)
      ]);

      if (!providerResponse.ok || !modelResponse.ok || !timeSeriesResponse.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const providerData = await providerResponse.json();
      const modelData = await modelResponse.json();
      const timeSeriesResult = await timeSeriesResponse.json();
      const previousPeriodResult = previousPeriodResponse.ok ? await previousPeriodResponse.json() : null;

      setAnalyticsData(providerData);
      setModelAnalytics(modelData);
      setPreviousPeriodData(previousPeriodResult);

      // Process time series data
      const processedTimeSeries: TimeSeriesData[] = timeSeriesResult.grouped_data.map((item: any) => ({
        period: item.period,
        cost: item.cost,
        requests: item.requests,
        tokens: item.input_tokens + item.output_tokens
      }));
      setTimeSeriesData(processedTimeSeries);

    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
    }
  }, [timeRange, selectedConfig, startDate, endDate]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData, timeRange, selectedConfig, startDate, endDate]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return { percentage: 0, isPositive: true };
    const percentage = ((current - previous) / previous) * 100;
    return { percentage: Math.abs(percentage), isPositive: percentage >= 0 };
  };

  const generateCostAlerts = (): CostAlert[] => {
    const alerts: CostAlert[] = [];
    const summary = analyticsData?.summary;

    if (!summary) return alerts;

    // Budget alert
    const projectedMonthlyCost = (summary.total_cost / parseInt(timeRange)) * 30;
    if (projectedMonthlyCost > monthlyBudget * 0.8) {
      alerts.push({
        type: projectedMonthlyCost > monthlyBudget ? 'danger' : 'warning',
        title: projectedMonthlyCost > monthlyBudget ? 'Budget Exceeded' : 'Budget Warning',
        message: `Projected monthly cost: ${formatCurrency(projectedMonthlyCost)} (Budget: ${formatCurrency(monthlyBudget)})`,
        value: `${((projectedMonthlyCost / monthlyBudget) * 100).toFixed(0)}%`
      });
    }

    // Success rate alert
    if (summary.success_rate < 95) {
      alerts.push({
        type: summary.success_rate < 90 ? 'danger' : 'warning',
        title: 'Low Success Rate',
        message: `Current success rate is ${summary.success_rate.toFixed(1)}%. Consider reviewing failed requests.`,
        value: `${summary.success_rate.toFixed(1)}%`
      });
    }

    // High cost per request alert
    if (summary.average_cost_per_request > 0.01) {
      alerts.push({
        type: 'warning',
        title: 'High Cost Per Request',
        message: `Average cost per request is ${formatCurrency(summary.average_cost_per_request)}. Consider optimizing model usage.`,
        value: formatCurrency(summary.average_cost_per_request)
      });
    }

    return alerts;
  };

  const generateRecommendations = (): UsageRecommendation[] => {
    const recommendations: UsageRecommendation[] = [];
    const summary = analyticsData?.summary;
    const providers = analyticsData?.grouped_data || [];
    const models = modelAnalytics?.grouped_data || [];

    if (!summary) return recommendations;

    // Cost optimization recommendations
    const mostExpensiveModel = models.sort((a, b) => b.cost - a.cost)[0];
    if (mostExpensiveModel && mostExpensiveModel.cost > summary.total_cost * 0.3) {
      const potentialSavings = mostExpensiveModel.cost * 0.2; // Assume 20% savings possible
      recommendations.push({
        type: 'cost_optimization',
        title: 'Optimize Expensive Model Usage',
        description: `${mostExpensiveModel.name} accounts for ${((mostExpensiveModel.cost / summary.total_cost) * 100).toFixed(1)}% of your costs. Consider using a more cost-effective alternative for simpler tasks.`,
        potential_savings: potentialSavings,
        icon: CurrencyDollarIcon
      });
    }

    // Performance recommendations
    if (summary.success_rate < 98) {
      recommendations.push({
        type: 'performance',
        title: 'Improve Request Reliability',
        description: `Your success rate is ${summary.success_rate.toFixed(1)}%. Implement retry logic and error handling to improve reliability.`,
        icon: CheckCircleIcon
      });
    }

    // Efficiency recommendations
    const avgTokensPerRequest = (summary.total_input_tokens + summary.total_output_tokens) / summary.total_requests;
    if (avgTokensPerRequest > 1000) {
      recommendations.push({
        type: 'efficiency',
        title: 'Optimize Token Usage',
        description: `Average ${formatNumber(avgTokensPerRequest)} tokens per request. Consider breaking down large prompts or using more efficient models.`,
        icon: CpuChipIcon
      });
    }

    return recommendations;
  };

  const resetFilters = () => {
    setTimeRange('30');
    setSelectedConfig('');
    setStartDate('');
    setEndDate('');
  };

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <h1 className="text-4xl font-bold mb-6">📊 Advanced Analytics</h1>
        <div className="card p-6 text-center">
          <p className="text-red-600 mb-4">Error loading analytics: {error}</p>
          <button onClick={fetchAnalyticsData} className="btn-primary">
            Retry
          </button>
        </div>
      </div>
    );
  }

  const summary = analyticsData?.summary;
  const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;
  const projectedMonthlyCost = summary ? (summary.total_cost / parseInt(timeRange)) * 30 : 0;

  // Generate insights
  const costAlerts = generateCostAlerts();
  const recommendations = generateRecommendations();

  // Calculate trends vs previous period
  const previousSummary = previousPeriodData?.summary;
  const costTrend = previousSummary ? calculateTrend(summary?.total_cost || 0, previousSummary.total_cost) : null;
  const requestTrend = previousSummary ? calculateTrend(summary?.total_requests || 0, previousSummary.total_requests) : null;

  // Professional Line Chart Component (like reference)
  const ProfessionalLineChart = ({ data }: { data: TimeSeriesData[] }) => {
    if (!data.length) {
      return (
        <div className="h-64 flex items-center justify-center">
          <div className="text-center">
            <ChartBarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-300">No data available</p>
          </div>
        </div>
      );
    }

    const maxCost = Math.max(...data.map(d => d.cost));
    const minCost = Math.min(...data.map(d => d.cost));
    const maxRequests = Math.max(...data.map(d => d.requests));
    const minRequests = Math.min(...data.map(d => d.requests));

    const costRange = maxCost - minCost || 1;
    const requestRange = maxRequests - minRequests || 1;

    return (
      <div className="relative h-64 w-full">
        <svg className="w-full h-full" viewBox="0 0 600 200">
          <defs>
            {/* Gradients for smooth fills */}
            <linearGradient id="costGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.05" />
            </linearGradient>
            <linearGradient id="requestGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.05" />
            </linearGradient>

            {/* Subtle grid pattern */}
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f1f5f9" strokeWidth="0.5"/>
            </pattern>
          </defs>

          {/* Grid background */}
          <rect width="600" height="200" fill="url(#grid)" />

          {/* Cost area fill */}
          <polygon
            fill="url(#costGradient)"
            points={`0,200 ${data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.cost - minCost) / costRange) * 160;
              return `${x},${y}`;
            }).join(' ')} 600,200`}
          />

          {/* Request area fill */}
          <polygon
            fill="url(#requestGradient)"
            points={`0,200 ${data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.requests - minRequests) / requestRange) * 160;
              return `${x},${y}`;
            }).join(' ')} 600,200`}
          />

          {/* Cost line - Purple like reference */}
          <polyline
            fill="none"
            stroke="#8b5cf6"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            points={data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.cost - minCost) / costRange) * 160;
              return `${x},${y}`;
            }).join(' ')}
          />

          {/* Request line - Cyan like reference */}
          <polyline
            fill="none"
            stroke="#06b6d4"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
            points={data.map((d, i) => {
              const x = (i / Math.max(data.length - 1, 1)) * 600;
              const y = 200 - ((d.requests - minRequests) / requestRange) * 160;
              return `${x},${y}`;
            }).join(' ')}
          />

          {/* Data points with hover effects */}
          {data.map((d, i) => {
            const x = (i / Math.max(data.length - 1, 1)) * 600;
            const costY = 200 - ((d.cost - minCost) / costRange) * 160;
            const requestY = 200 - ((d.requests - minRequests) / requestRange) * 160;
            return (
              <g key={i}>
                <circle
                  cx={x}
                  cy={costY}
                  r="3"
                  fill="#8b5cf6"
                  stroke="white"
                  strokeWidth="2"
                  className="hover:r-5 transition-all duration-200 cursor-pointer"
                >
                  <title>{`${d.period}: ${formatCurrency(d.cost)}`}</title>
                </circle>
                <circle
                  cx={x}
                  cy={requestY}
                  r="3"
                  fill="#06b6d4"
                  stroke="white"
                  strokeWidth="2"
                  className="hover:r-5 transition-all duration-200 cursor-pointer"
                >
                  <title>{`${d.period}: ${formatNumber(d.requests)} requests`}</title>
                </circle>
              </g>
            );
          })}
        </svg>
      </div>
    );
  };

  // Professional Donut Chart Component (like reference)
  const ProfessionalDonutChart = ({ data, total }: { data: any[], total: number }) => {
    if (!data.length) {
      return (
        <div className="h-48 flex items-center justify-center">
          <div className="text-center">
            <ChartPieIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-300">No data available</p>
          </div>
        </div>
      );
    }

    // Professional color palette like the reference
    const colors = ['#f43f5e', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b', '#ef4444'];
    const radius = 70;
    const strokeWidth = 16;
    const normalizedRadius = radius - strokeWidth * 0.5;
    const circumference = normalizedRadius * 2 * Math.PI;

    let cumulativePercentage = 0;

    return (
      <div className="flex flex-col items-center">
        <div className="relative mb-6">
          <svg width="160" height="160" className="transform -rotate-90">
            {/* Background circle with subtle shadow */}
            <circle
              cx="80"
              cy="80"
              r={normalizedRadius}
              stroke="#f8fafc"
              strokeWidth={strokeWidth}
              fill="transparent"
            />

            {/* Data segments */}
            {data.map((item, index) => {
              const percentage = (item.cost / total) * 100;
              const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
              const strokeDashoffset = -((cumulativePercentage / 100) * circumference);

              cumulativePercentage += percentage;

              return (
                <circle
                  key={index}
                  cx="80"
                  cy="80"
                  r={normalizedRadius}
                  stroke={colors[index % colors.length]}
                  strokeWidth={strokeWidth}
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  fill="transparent"
                  strokeLinecap="round"
                  className="transition-all duration-300 hover:opacity-80 cursor-pointer"
                  style={{
                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                  }}
                />
              );
            })}
          </svg>

          {/* Clean center - no ugly cost display */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{data.length}</div>
              <div className="text-sm text-gray-300">Providers</div>
            </div>
          </div>
        </div>

        {/* Clean Legend */}
        <div className="space-y-2 w-full">
          {data.slice(0, 4).map((item, index) => {
            const percentage = ((item.cost / total) * 100).toFixed(1);
            return (
              <div key={index} className="flex items-center justify-between py-2 px-3 rounded-lg hover:bg-white/5 transition-colors duration-150">
                <div className="flex items-center">
                  <div
                    className="w-3 h-3 rounded-full mr-3 shadow-sm"
                    style={{ backgroundColor: colors[index] }}
                  ></div>
                  <span className="text-sm font-medium text-white capitalize">{item.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-white">{percentage}%</div>
                  <div className="text-xs text-gray-400">{formatCurrency(item.cost)}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-[#040716] p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-white mb-2">Analytics</h1>

          {/* Navigation Tabs */}
          <div className="flex items-center space-x-1 mb-6">
            <button className="px-4 py-2 bg-cyan-500 text-white rounded-lg text-sm font-medium">
              Overview
            </button>
            <button className="px-4 py-2 text-gray-400 hover:text-white text-sm font-medium">
              Users
            </button>
            <button className="px-4 py-2 text-gray-400 hover:text-white text-sm font-medium">
              Errors
            </button>
            <button className="px-4 py-2 text-gray-400 hover:text-white text-sm font-medium">
              Cache
            </button>
            <button className="px-4 py-2 text-gray-400 hover:text-white text-sm font-medium">
              Feedback
            </button>
            <button className="px-4 py-2 text-gray-400 hover:text-white text-sm font-medium">
              Metadata
            </button>
          </div>

          {/* Search and Date Filter */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search Filter"
                  className="bg-[#1B1C1D] border border-gray-700 text-white rounded-lg px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-cyan-500 w-64"
                />
                <svg className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="bg-[#1B1C1D] border border-gray-700 text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-cyan-500"
              >
                <option value="7">2023-07-06 - Dec 2023</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
              </select>
            </div>
          </div>
        </div>

        {/* Top Metric Cards - Like N8N Reference */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Total Request Made */}
            <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-1">Total Request Made</h3>
                  <div className="flex items-center">
                    <span className="text-3xl font-bold text-white mr-3">
                      {formatNumber(summary.total_requests)}
                    </span>
                    {requestTrend && (
                      <div className={`flex items-center px-2 py-1 rounded text-xs font-medium ${
                        requestTrend.isPositive ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                      }`}>
                        {requestTrend.isPositive ? '+' : ''}{requestTrend.percentage.toFixed(1)}%
                      </div>
                    )}
                  </div>
                </div>
                <div className="p-3 bg-blue-500/20 rounded-lg">
                  <ChartBarIcon className="h-6 w-6 text-blue-400" />
                </div>
              </div>
            </div>

            {/* Average Latency */}
            <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-1">Average Latency</h3>
                  <div className="flex items-center">
                    <span className="text-3xl font-bold text-white mr-3">
                      {summary?.average_latency ? `${summary.average_latency.toFixed(0)}ms` : '144ms'}
                    </span>
                    <div className="flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400">
                      +20.34%
                    </div>
                  </div>
                </div>
                <div className="p-3 bg-orange-500/20 rounded-lg">
                  <ClockIcon className="h-6 w-6 text-orange-400" />
                </div>
              </div>
            </div>

            {/* User Feedback */}
            <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-1">User Feedback</h3>
                  <div className="flex items-center">
                    <span className="text-3xl font-bold text-white mr-3">
                      {summary.success_rate.toFixed(1)}%
                    </span>
                    <div className={`flex items-center px-2 py-1 rounded text-xs font-medium ${
                      summary.success_rate >= 95 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                    }`}>
                      {summary.success_rate >= 95 ? '+1.34%' : '-1.34%'}
                    </div>
                  </div>
                </div>
                <div className="p-3 bg-green-500/20 rounded-lg">
                  <HandThumbUpIcon className="h-6 w-6 text-green-400" />
                </div>
              </div>
            </div>

            {/* Total Cost */}
            <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-400 mb-1">Cost</h3>
                  <div className="flex items-center">
                    <span className="text-3xl font-bold text-white mr-3">
                      {formatCurrency(summary.total_cost)}
                    </span>
                    {costTrend && (
                      <div className={`flex items-center px-2 py-1 rounded text-xs font-medium ${
                        costTrend.isPositive ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'
                      }`}>
                        {costTrend.isPositive ? '+' : ''}{costTrend.percentage.toFixed(1)}%
                      </div>
                    )}
                  </div>
                </div>
                <div className="p-3 bg-purple-500/20 rounded-lg">
                  <CurrencyDollarIcon className="h-6 w-6 text-purple-400" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Charts Section - 6 Chart Grid Like N8N Reference */}
        {/* 6-Chart Grid Layout Like N8N Reference */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Cost Chart */}
          <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-white font-medium">Cost</h3>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold text-white mr-3">
                    {formatCurrency(summary?.total_cost || 98492)}
                  </span>
                  <div className="flex items-center px-2 py-1 rounded text-xs font-medium bg-red-500/20 text-red-400">
                    -10.46%
                  </div>
                </div>
              </div>
              <div className="p-2 bg-gray-700 rounded">
                <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            {/* Bar Chart - More realistic like N8N */}
            <div className="h-32 flex items-end space-x-1">
              {[...Array(24)].map((_, i) => (
                <div
                  key={i}
                  className="bg-green-500 rounded-t transition-all duration-300 hover:bg-green-400"
                  style={{
                    height: `${Math.random() * 70 + 30}%`,
                    width: '3.5%'
                  }}
                />
              ))}
            </div>
          </div>

          {/* Latency Chart */}
          <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-white font-medium">Latency</h3>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold text-white mr-3">
                    {summary?.average_latency ? `${summary.average_latency.toFixed(0)}ms` : '29ms'}
                  </span>
                  <div className="flex items-center px-2 py-1 rounded text-xs font-medium bg-orange-500/20 text-orange-400">
                    112%
                  </div>
                </div>
              </div>
              <div className="p-2 bg-gray-700 rounded">
                <ClockIcon className="h-4 w-4 text-gray-400" />
              </div>
            </div>
            {/* Line Chart - More realistic like N8N */}
            <div className="h-32 relative">
              <svg className="w-full h-full" viewBox="0 0 300 120">
                <defs>
                  <linearGradient id="latencyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.3"/>
                    <stop offset="100%" stopColor="#f59e0b" stopOpacity="0"/>
                  </linearGradient>
                </defs>
                <path
                  d="M0,80 Q50,40 100,60 T200,30 T300,50"
                  stroke="#f59e0b"
                  strokeWidth="3"
                  fill="url(#latencyGradient)"
                />
                <path
                  d="M0,80 Q50,40 100,60 T200,30 T300,50"
                  stroke="#f59e0b"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </div>
          </div>

          {/* Tokens Used Chart */}
          <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-white font-medium">Tokens Used</h3>
                <p className="text-sm text-gray-400">March 28</p>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold text-white mr-3">
                    {summary ? `${((summary.total_input_tokens + summary.total_output_tokens) / 1000000).toFixed(1)}M` : '25.4M'}
                  </span>
                  <div className="flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400">
                    +9.39%
                  </div>
                </div>
              </div>
              <div className="p-2 bg-gray-700 rounded">
                <CpuChipIcon className="h-4 w-4 text-gray-400" />
              </div>
            </div>
            {/* Scatter Plot - More realistic like N8N */}
            <div className="h-32 relative">
              <svg className="w-full h-full" viewBox="0 0 300 120">
                {[...Array(40)].map((_, i) => (
                  <circle
                    key={i}
                    cx={Math.random() * 280 + 10}
                    cy={Math.random() * 100 + 10}
                    r={Math.random() * 3 + 1}
                    fill={['#10b981', '#3b82f6', '#f59e0b', '#8b5cf6'][Math.floor(Math.random() * 4)]}
                    opacity="0.8"
                    className="hover:opacity-100 transition-opacity duration-200"
                  />
                ))}
              </svg>
            </div>
          </div>

          {/* Requests Chart */}
          <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-white font-medium">Requests</h3>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold text-white mr-3">
                    {formatNumber(summary?.total_requests || 63800)}
                  </span>
                  <div className="flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400">
                    +3.39%
                  </div>
                </div>
              </div>
              <div className="p-2 bg-gray-700 rounded">
                <ChartBarIcon className="h-4 w-4 text-gray-400" />
              </div>
            </div>
            {/* Horizontal Bar Chart - More realistic like N8N */}
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <div className="w-full bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-pink-500 to-green-500 h-3 rounded-full transition-all duration-500 hover:from-pink-400 hover:to-green-400"
                      style={{ width: `${Math.random() * 70 + 30}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Unique Users Chart */}
          <div className="bg-[#1B1C1D] border border-gray-700 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-white font-medium">Unique Users</h3>
                <div className="flex items-center mt-2">
                  <span className="text-2xl font-bold text-white mr-3">78</span>
                  <div className="flex items-center px-2 py-1 rounded text-xs font-medium bg-green-500/20 text-green-400">
                    +3.39%
                  </div>
                </div>
              </div>
              <div className="p-2 bg-gray-700 rounded">
                <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
            {/* Wave Chart - More realistic like N8N */}
            <div className="h-32 relative">
              <svg className="w-full h-full" viewBox="0 0 300 120">
                <defs>
                  <linearGradient id="userGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.3"/>
                    <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0"/>
                  </linearGradient>
                </defs>
                <path
                  d="M0,90 Q30,50 60,70 Q90,40 120,60 Q150,30 180,50 Q210,70 240,40 Q270,60 300,50"
                  stroke="#8b5cf6"
                  strokeWidth="3"
                  fill="url(#userGradient)"
                />
                <path
                  d="M0,90 Q30,50 60,70 Q90,40 120,60 Q150,30 180,50 Q210,70 240,40 Q270,60 300,50"
                  stroke="#8b5cf6"
                  strokeWidth="2"
                  fill="none"
                />
              </svg>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default function AnalyticsPage() {
  return (
    <Suspense fallback={
      <div className="space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    }>
      <AnalyticsPageContent />
    </Suspense>
  );
}

        {/* Detailed Analytics Tables */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Provider Performance Table */}
          <div className="bg-[#2A2B2C] border border-white/10 rounded-2xl p-6">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-1">Provider Performance</h3>
              <p className="text-sm text-gray-400">Detailed breakdown by provider</p>
            </div>
            {analyticsData?.grouped_data.length ? (
              <div className="overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="text-left py-3 text-sm font-medium text-gray-400">Provider</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-400">Cost</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-400">Requests</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-400">Share</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/5">
                    {analyticsData.grouped_data
                      .sort((a, b) => b.cost - a.cost)
                      .map((provider, index) => {
                        const colors = ['#ff6b35', '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
                        const percentage = ((provider.cost / (summary?.total_cost || 1)) * 100).toFixed(1);
                        return (
                          <tr key={provider.name} className="hover:bg-white/5 transition-colors duration-150">
                            <td className="py-4">
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-3"
                                  style={{ backgroundColor: colors[index % colors.length] }}
                                ></div>
                                <span className="font-medium text-white capitalize">{provider.name}</span>
                              </div>
                            </td>
                            <td className="py-4 text-right font-semibold text-white">
                              {formatCurrency(provider.cost)}
                            </td>
                            <td className="py-4 text-right text-gray-400">
                              {formatNumber(provider.requests)}
                            </td>
                            <td className="py-4 text-right">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/10 text-gray-300">
                                {percentage}%
                              </span>
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-300">
                <ChartPieIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No provider data available</p>
              </div>
            )}
          </div>

          {/* Model Performance Table */}
          <div className="bg-[#2A2B2C] border border-white/10 rounded-2xl p-6">
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-1">Top Models</h3>
              <p className="text-sm text-gray-400">Most expensive models by cost</p>
            </div>

            {modelAnalytics?.grouped_data.length ? (
              <div className="overflow-hidden">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/10">
                      <th className="text-left py-3 text-sm font-medium text-gray-400">Model</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-400">Cost</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-400">Requests</th>
                      <th className="text-right py-3 text-sm font-medium text-gray-400">Avg/Request</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/5">
                    {modelAnalytics.grouped_data
                      .sort((a, b) => b.cost - a.cost)
                      .slice(0, 5)
                      .map((model, index) => {
                        const colors = ['#ff6b35', '#3b82f6', '#10b981', '#f59e0b', '#ef4444'];
                        const avgCost = model.cost / Math.max(model.requests, 1);
                        return (
                          <tr key={model.name} className="hover:bg-white/5 transition-colors duration-150">
                            <td className="py-4">
                              <div className="flex items-center">
                                <div
                                  className="w-3 h-3 rounded-full mr-3"
                                  style={{ backgroundColor: colors[index % colors.length] }}
                                ></div>
                                <div>
                                  <span className="font-medium text-white">{model.name.length > 20 ? model.name.substring(0, 20) + '...' : model.name}</span>
                                  <div className="text-xs text-gray-400 mt-1">
                                    {formatNumber(model.input_tokens + model.output_tokens)} tokens
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="py-4 text-right font-semibold text-white">
                              {formatCurrency(model.cost)}
                            </td>
                            <td className="py-4 text-right text-gray-300">
                              {formatNumber(model.requests)}
                            </td>
                            <td className="py-4 text-right text-gray-300">
                              {formatCurrency(avgCost)}
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-300">
                <CpuChipIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No model data available</p>
              </div>
            )}
          </div>
        </div>








      </div>
    </div>
  );
}

export default function AnalyticsPage() {
  return (
    <Suspense fallback={
      <div className="space-y-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>
      </div>
    }>
      <AnalyticsPageContent />
    </Suspense>
  );
}
